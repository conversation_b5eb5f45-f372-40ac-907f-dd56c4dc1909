import { Router } from 'express';
import { CreateProjectRequestSchema, UpdateProjectRequestSchema } from '@waatch/shared';
import { asyncHandler } from '../middleware/errorHandler';
import { authenticateToken } from '../middleware/auth';
import { ProjectService } from '../services/project';
import { logger } from '../utils/logger';

const router = Router();
const projectService = new ProjectService();

// Apply authentication to all project routes
router.use(authenticateToken);

/**
 * @swagger
 * /api/projects:
 *   get:
 *     summary: Get user's projects
 *     tags: [Projects]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *       - in: query
 *         name: offset
 *         schema:
 *           type: integer
 *           default: 0
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search projects by name or description
 *     responses:
 *       200:
 *         description: List of projects
 */
router.get('/', asyncHandler(async (req, res) => {
  const userId = req.user!.id;
  const limit = parseInt(req.query.limit as string) || 20;
  const offset = parseInt(req.query.offset as string) || 0;
  const search = req.query.search as string;

  const projects = await projectService.getUserProjects(userId, limit, offset, search);
  
  logger.info('Projects list requested', {
    userId,
    projectCount: projects.length,
    limit,
    offset,
    search,
  });
  
  res.json({ projects });
}));

/**
 * @swagger
 * /api/projects:
 *   post:
 *     summary: Create new project
 *     tags: [Projects]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               framework:
 *                 type: string
 *               language:
 *                 type: string
 *               templateId:
 *                 type: string
 *               settings:
 *                 type: object
 *     responses:
 *       201:
 *         description: Project created successfully
 */
router.post('/', asyncHandler(async (req, res) => {
  const userId = req.user!.id;
  const projectData = CreateProjectRequestSchema.parse(req.body);

  const project = await projectService.createProject(userId, projectData);
  
  logger.info('Project created', {
    userId,
    projectId: project.id,
    name: project.name,
    framework: project.framework,
    language: project.language,
  });
  
  res.status(201).json({ project });
}));

/**
 * @swagger
 * /api/projects/{projectId}:
 *   get:
 *     summary: Get project details
 *     tags: [Projects]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: projectId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Project details
 *       404:
 *         description: Project not found
 */
router.get('/:projectId', asyncHandler(async (req, res) => {
  const userId = req.user!.id;
  const { projectId } = req.params;

  const project = await projectService.getProject(projectId, userId);
  
  res.json({ project });
}));

/**
 * @swagger
 * /api/projects/{projectId}:
 *   put:
 *     summary: Update project
 *     tags: [Projects]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: projectId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               framework:
 *                 type: string
 *               language:
 *                 type: string
 *               settings:
 *                 type: object
 *     responses:
 *       200:
 *         description: Project updated successfully
 */
router.put('/:projectId', asyncHandler(async (req, res) => {
  const userId = req.user!.id;
  const { projectId } = req.params;
  const updateData = UpdateProjectRequestSchema.parse(req.body);

  const project = await projectService.updateProject(projectId, userId, updateData);
  
  logger.info('Project updated', {
    userId,
    projectId,
    updates: Object.keys(updateData),
  });
  
  res.json({ project });
}));

/**
 * @swagger
 * /api/projects/{projectId}:
 *   delete:
 *     summary: Delete project
 *     tags: [Projects]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: projectId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Project deleted successfully
 */
router.delete('/:projectId', asyncHandler(async (req, res) => {
  const userId = req.user!.id;
  const { projectId } = req.params;

  await projectService.deleteProject(projectId, userId);
  
  logger.info('Project deleted', {
    userId,
    projectId,
  });
  
  res.json({ message: 'Project deleted successfully' });
}));

/**
 * @swagger
 * /api/projects/{projectId}/files:
 *   get:
 *     summary: Get project files
 *     tags: [Projects]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: projectId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Project file structure
 */
router.get('/:projectId/files', asyncHandler(async (req, res) => {
  const userId = req.user!.id;
  const { projectId } = req.params;

  const files = await projectService.getProjectFiles(projectId, userId);
  
  res.json({ files });
}));

/**
 * @swagger
 * /api/projects/{projectId}/export:
 *   post:
 *     summary: Export project
 *     tags: [Projects]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: projectId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - format
 *             properties:
 *               format:
 *                 type: string
 *                 enum: [zip, tar, git]
 *               includeNodeModules:
 *                 type: boolean
 *                 default: false
 *               includeDotFiles:
 *                 type: boolean
 *                 default: true
 *     responses:
 *       200:
 *         description: Export URL or download link
 */
router.post('/:projectId/export', asyncHandler(async (req, res) => {
  const userId = req.user!.id;
  const { projectId } = req.params;
  const exportRequest = req.body;

  const exportResult = await projectService.exportProject(projectId, userId, exportRequest);
  
  logger.info('Project export requested', {
    userId,
    projectId,
    format: exportRequest.format,
  });
  
  res.json(exportResult);
}));

export { router as projectRoutes };
