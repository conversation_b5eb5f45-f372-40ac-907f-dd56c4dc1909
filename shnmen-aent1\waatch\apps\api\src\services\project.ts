import { 
  Project, 
  CreateProjectRequest, 
  UpdateProjectRequest,
  ExportRequest,
  FileNode,
  NotFoundError,
  AuthorizationError
} from '@waatch/shared';
import { prisma } from './database';
import { logger } from '../utils/logger';

export class ProjectService {
  // Get user's projects
  async getUserProjects(
    userId: string, 
    limit: number = 20, 
    offset: number = 0,
    search?: string
  ): Promise<Project[]> {
    const whereClause: any = { userId };
    
    if (search) {
      whereClause.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
      ];
    }

    const projects = await prisma.project.findMany({
      where: whereClause,
      orderBy: { updatedAt: 'desc' },
      take: limit,
      skip: offset,
    });

    return projects.map(this.mapPrismaToProject);
  }

  // Create new project
  async createProject(userId: string, data: CreateProjectRequest): Promise<Project> {
    const project = await prisma.project.create({
      data: {
        userId,
        name: data.name,
        description: data.description,
        framework: data.framework,
        language: data.language,
        templateId: data.templateId,
        settings: data.settings || {},
        files: this.getInitialFiles(data.framework, data.language),
      },
    });

    logger.info('Project created', {
      projectId: project.id,
      userId,
      name: data.name,
      framework: data.framework,
      language: data.language,
    });

    return this.mapPrismaToProject(project);
  }

  // Get project by ID
  async getProject(projectId: string, userId: string): Promise<Project> {
    const project = await prisma.project.findFirst({
      where: { 
        id: projectId,
        userId,
      },
    });

    if (!project) {
      throw new NotFoundError('Project');
    }

    return this.mapPrismaToProject(project);
  }

  // Update project
  async updateProject(
    projectId: string, 
    userId: string, 
    data: UpdateProjectRequest
  ): Promise<Project> {
    // Verify ownership
    await this.getProject(projectId, userId);

    const project = await prisma.project.update({
      where: { id: projectId },
      data: {
        name: data.name,
        description: data.description,
        framework: data.framework,
        language: data.language,
        settings: data.settings,
        updatedAt: new Date(),
      },
    });

    return this.mapPrismaToProject(project);
  }

  // Delete project
  async deleteProject(projectId: string, userId: string): Promise<void> {
    // Verify ownership
    await this.getProject(projectId, userId);

    await prisma.project.delete({
      where: { id: projectId },
    });
  }

  // Get project files
  async getProjectFiles(projectId: string, userId: string): Promise<FileNode[]> {
    const project = await this.getProject(projectId, userId);
    
    // Convert stored files to FileNode structure
    return this.convertFilesToNodes(project.files);
  }

  // Update project files
  async updateProjectFiles(
    projectId: string, 
    userId: string, 
    files: Record<string, any>
  ): Promise<Project> {
    // Verify ownership
    await this.getProject(projectId, userId);

    const project = await prisma.project.update({
      where: { id: projectId },
      data: {
        files,
        updatedAt: new Date(),
      },
    });

    return this.mapPrismaToProject(project);
  }

  // Export project
  async exportProject(
    projectId: string, 
    userId: string, 
    exportRequest: ExportRequest
  ): Promise<{ downloadUrl: string; expiresAt: Date }> {
    const project = await this.getProject(projectId, userId);

    // TODO: Implement actual export functionality
    // This would typically involve:
    // 1. Creating a temporary directory
    // 2. Writing all project files
    // 3. Creating archive (zip/tar)
    // 4. Uploading to storage service
    // 5. Returning download URL

    logger.info('Project export requested', {
      projectId,
      userId,
      format: exportRequest.format,
      includeNodeModules: exportRequest.includeNodeModules,
      includeDotFiles: exportRequest.includeDotFiles,
    });

    // Placeholder implementation
    const downloadUrl = `https://exports.waatch.dev/${projectId}/${Date.now()}.${exportRequest.format}`;
    const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

    return {
      downloadUrl,
      expiresAt,
    };
  }

  // Get initial files based on framework and language
  private getInitialFiles(framework?: string, language?: string): Record<string, any> {
    const files: Record<string, any> = {};

    // Default files
    files['README.md'] = {
      content: `# New Project\n\nGenerated with Waatch AI Platform\n`,
      language: 'markdown',
    };

    files['.gitignore'] = {
      content: this.getGitignoreContent(framework, language),
      language: 'gitignore',
    };

    // Framework-specific files
    if (framework === 'react') {
      files['package.json'] = {
        content: JSON.stringify({
          name: 'waatch-project',
          version: '1.0.0',
          private: true,
          dependencies: {
            react: '^18.2.0',
            'react-dom': '^18.2.0',
          },
          scripts: {
            start: 'react-scripts start',
            build: 'react-scripts build',
            test: 'react-scripts test',
          },
        }, null, 2),
        language: 'json',
      };

      files['src/App.tsx'] = {
        content: `import React from 'react';\n\nfunction App() {\n  return (\n    <div className="App">\n      <h1>Hello Waatch!</h1>\n    </div>\n  );\n}\n\nexport default App;\n`,
        language: 'typescript',
      };

      files['src/index.tsx'] = {
        content: `import React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport App from './App';\n\nconst root = ReactDOM.createRoot(\n  document.getElementById('root') as HTMLElement\n);\nroot.render(<App />);\n`,
        language: 'typescript',
      };

      files['public/index.html'] = {
        content: `<!DOCTYPE html>\n<html lang="en">\n<head>\n  <meta charset="utf-8" />\n  <meta name="viewport" content="width=device-width, initial-scale=1" />\n  <title>Waatch Project</title>\n</head>\n<body>\n  <div id="root"></div>\n</body>\n</html>\n`,
        language: 'html',
      };
    }

    if (framework === 'express') {
      files['package.json'] = {
        content: JSON.stringify({
          name: 'waatch-api',
          version: '1.0.0',
          main: 'index.js',
          dependencies: {
            express: '^4.18.2',
          },
          scripts: {
            start: 'node index.js',
            dev: 'nodemon index.js',
          },
        }, null, 2),
        language: 'json',
      };

      files['index.js'] = {
        content: `const express = require('express');\nconst app = express();\nconst port = process.env.PORT || 3000;\n\napp.get('/', (req, res) => {\n  res.json({ message: 'Hello from Waatch API!' });\n});\n\napp.listen(port, () => {\n  console.log(\`Server running on port \${port}\`);\n});\n`,
        language: 'javascript',
      };
    }

    return files;
  }

  // Get gitignore content based on framework and language
  private getGitignoreContent(framework?: string, language?: string): string {
    let content = `# Dependencies\nnode_modules/\n\n# Logs\n*.log\nnpm-debug.log*\n\n# Environment variables\n.env\n.env.local\n\n# Build outputs\ndist/\nbuild/\n\n# IDE\n.vscode/\n.idea/\n\n# OS\n.DS_Store\nThumbs.db\n`;

    if (framework === 'react') {
      content += `\n# React\n.next/\nout/\n\n# Testing\ncoverage/\n`;
    }

    if (language === 'python') {
      content += `\n# Python\n__pycache__/\n*.pyc\n*.pyo\n*.pyd\n.Python\nvenv/\n.venv/\n`;
    }

    return content;
  }

  // Convert files object to FileNode array
  private convertFilesToNodes(files: Record<string, any>): FileNode[] {
    const nodes: FileNode[] = [];
    const pathMap = new Map<string, FileNode>();

    // Sort paths to ensure directories are created before files
    const sortedPaths = Object.keys(files).sort();

    for (const path of sortedPaths) {
      const parts = path.split('/');
      const fileName = parts[parts.length - 1];
      const isFile = fileName.includes('.');

      const node: FileNode = {
        id: path,
        name: fileName,
        path,
        type: isFile ? 'file' : 'directory',
        lastModified: new Date(),
        ...(isFile && {
          content: files[path].content,
          language: files[path].language,
          size: files[path].content?.length || 0,
        }),
        ...(!isFile && { children: [] }),
      };

      pathMap.set(path, node);

      // Find parent directory
      if (parts.length > 1) {
        const parentPath = parts.slice(0, -1).join('/');
        const parent = pathMap.get(parentPath);
        if (parent && parent.children) {
          parent.children.push(node);
        }
      } else {
        nodes.push(node);
      }
    }

    return nodes;
  }

  // Map Prisma model to Project type
  private mapPrismaToProject(prismaProject: any): Project {
    return {
      id: prismaProject.id,
      userId: prismaProject.userId,
      sessionId: prismaProject.sessionId,
      name: prismaProject.name,
      description: prismaProject.description,
      files: prismaProject.files as Record<string, any>,
      framework: prismaProject.framework,
      language: prismaProject.language,
      templateId: prismaProject.templateId,
      settings: prismaProject.settings as Record<string, any>,
      createdAt: prismaProject.createdAt,
      updatedAt: prismaProject.updatedAt,
    };
  }
}
