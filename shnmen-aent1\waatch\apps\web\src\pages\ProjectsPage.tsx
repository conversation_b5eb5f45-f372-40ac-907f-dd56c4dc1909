import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { 
  FolderOpen, 
  Plus, 
  Search, 
  Filter,
  Calendar,
  Code,
  ExternalLink
} from 'lucide-react';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';

interface Project {
  id: string;
  name: string;
  description?: string;
  framework?: string;
  language?: string;
  createdAt: Date;
  updatedAt: Date;
}

export function ProjectsPage() {
  const { projectId } = useParams();
  const [projects, setProjects] = useState<Project[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    // TODO: Fetch projects from API
    const fetchProjects = async () => {
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        setProjects([
          {
            id: '1',
            name: 'E-commerce Dashboard',
            description: 'React dashboard for managing online store',
            framework: 'React',
            language: 'TypeScript',
            createdAt: new Date('2024-01-15'),
            updatedAt: new Date('2024-01-20'),
          },
          {
            id: '2',
            name: 'API Gateway Service',
            description: 'Node.js microservice for API routing',
            framework: 'Express',
            language: 'JavaScript',
            createdAt: new Date('2024-01-10'),
            updatedAt: new Date('2024-01-18'),
          },
          {
            id: '3',
            name: 'Mobile App Backend',
            description: 'REST API for mobile application',
            framework: 'FastAPI',
            language: 'Python',
            createdAt: new Date('2024-01-05'),
            updatedAt: new Date('2024-01-16'),
          },
        ]);
      } catch (error) {
        console.error('Failed to fetch projects:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchProjects();
  }, []);

  const filteredProjects = projects.filter(project =>
    project.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    project.description?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="p-6 lg:p-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
        <div>
          <h1 className="text-2xl font-bold text-foreground">Projects</h1>
          <p className="text-muted-foreground mt-1">
            Manage your AI-generated projects and code
          </p>
        </div>
        
        <button className="mt-4 sm:mt-0 inline-flex items-center px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 font-medium">
          <Plus className="h-4 w-4 mr-2" />
          New Project
        </button>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <input
            type="text"
            placeholder="Search projects..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary"
          />
        </div>
        
        <button className="inline-flex items-center px-4 py-2 border border-border rounded-md bg-background text-foreground hover:bg-accent">
          <Filter className="h-4 w-4 mr-2" />
          Filter
        </button>
      </div>

      {/* Projects Grid */}
      {filteredProjects.length === 0 ? (
        <div className="text-center py-12">
          <FolderOpen className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium text-foreground mb-2">
            {searchQuery ? 'No projects found' : 'No projects yet'}
          </h3>
          <p className="text-muted-foreground mb-4">
            {searchQuery 
              ? 'Try adjusting your search terms'
              : 'Create your first project to get started with AI-powered development'
            }
          </p>
          {!searchQuery && (
            <button className="inline-flex items-center px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 font-medium">
              <Plus className="h-4 w-4 mr-2" />
              Create Project
            </button>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredProjects.map((project) => (
            <div
              key={project.id}
              className="bg-card rounded-lg border border-border p-6 hover:shadow-md transition-shadow cursor-pointer"
            >
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="h-10 w-10 bg-primary/10 rounded-lg flex items-center justify-center">
                    <FolderOpen className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <h3 className="font-medium text-foreground">{project.name}</h3>
                    {project.description && (
                      <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
                        {project.description}
                      </p>
                    )}
                  </div>
                </div>
                
                <button className="p-1 hover:bg-accent rounded">
                  <ExternalLink className="h-4 w-4 text-muted-foreground" />
                </button>
              </div>

              <div className="flex items-center space-x-4 text-xs text-muted-foreground mb-4">
                {project.framework && (
                  <div className="flex items-center space-x-1">
                    <Code className="h-3 w-3" />
                    <span>{project.framework}</span>
                  </div>
                )}
                {project.language && (
                  <div className="flex items-center space-x-1">
                    <div className="h-2 w-2 bg-blue-500 rounded-full"></div>
                    <span>{project.language}</span>
                  </div>
                )}
              </div>

              <div className="flex items-center justify-between text-xs text-muted-foreground">
                <div className="flex items-center space-x-1">
                  <Calendar className="h-3 w-3" />
                  <span>Updated {project.updatedAt.toLocaleDateString()}</span>
                </div>
                
                <Link
                  to={`/projects/${project.id}`}
                  className="text-primary hover:text-primary/80 font-medium"
                >
                  Open
                </Link>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
