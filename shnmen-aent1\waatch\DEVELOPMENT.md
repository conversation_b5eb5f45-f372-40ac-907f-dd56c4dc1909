# Waatch Development Guide

This guide provides detailed instructions for setting up and developing the Waatch application.

## 🚀 Quick Start

### Prerequisites

Ensure you have the following installed:
- **Node.js 20.10+** LTS
- **pnpm 8.0+** (recommended package manager)
- **Docker & Docker Compose** (for databases)
- **Git** for version control

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd waatch
   ```

2. **Install dependencies**
   ```bash
   pnpm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   cp apps/web/.env.example apps/web/.env
   ```

4. **Start development databases**
   ```bash
   pnpm docker:dev
   ```

5. **Run database migrations**
   ```bash
   pnpm db:migrate
   ```

6. **Start development servers**
   ```bash
   pnpm dev
   ```

### Access Points

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:3001
- **API Documentation**: http://localhost:3001/api-docs
- **Database Admin (pgAdmin)**: http://localhost:5050
- **Redis Admin**: http://localhost:8081

## 🏗️ Project Structure

```
waatch/
├── apps/
│   ├── web/                 # React frontend
│   │   ├── src/
│   │   │   ├── components/  # React components
│   │   │   ├── pages/       # Page components
│   │   │   ├── stores/      # Zustand stores
│   │   │   ├── services/    # API services
│   │   │   └── utils/       # Utility functions
│   │   ├── public/          # Static assets
│   │   └── package.json
│   └── api/                 # Express.js backend
│       ├── src/
│       │   ├── routes/      # API routes
│       │   ├── services/    # Business logic
│       │   ├── middleware/  # Express middleware
│       │   ├── config/      # Configuration
│       │   └── utils/       # Utility functions
│       ├── prisma/          # Database schema
│       └── package.json
├── packages/
│   ├── shared/              # Shared types and utilities
│   ├── ui/                  # Shared UI components
│   └── config/              # Shared configurations
├── docker/                  # Docker configurations
└── docs/                    # Documentation
```

## 🛠️ Development Workflow

### Available Scripts

```bash
# Development
pnpm dev              # Start all development servers
pnpm build            # Build all applications
pnpm test             # Run all tests
pnpm lint             # Lint all code
pnpm type-check       # TypeScript type checking

# Database
pnpm db:generate      # Generate Prisma client
pnpm db:migrate       # Run database migrations
pnpm db:push          # Push schema changes
pnpm db:studio        # Open Prisma Studio
pnpm db:seed          # Seed database with test data

# Docker
pnpm docker:dev       # Start development containers
pnpm docker:down      # Stop development containers

# Utilities
pnpm clean            # Clean all build artifacts
pnpm format           # Format code with Prettier
```

### Code Style and Quality

The project enforces strict code quality standards:

- **TypeScript**: Strict mode enabled with comprehensive type checking
- **ESLint**: Extended rules for React, TypeScript, and Node.js
- **Prettier**: Consistent code formatting
- **Husky**: Pre-commit hooks for quality checks
- **Conventional Commits**: Standardized commit messages

### Git Workflow

1. **Create a feature branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make your changes**
   - Follow the coding standards
   - Write tests for new functionality
   - Update documentation as needed

3. **Commit your changes**
   ```bash
   git add .
   git commit -m "feat: add new feature"
   ```

4. **Push and create PR**
   ```bash
   git push origin feature/your-feature-name
   ```

## 🧪 Testing

### Frontend Testing

```bash
cd apps/web
pnpm test              # Run unit tests
pnpm test:watch        # Run tests in watch mode
pnpm test:coverage     # Generate coverage report
```

### Backend Testing

```bash
cd apps/api
pnpm test              # Run unit tests
pnpm test:watch        # Run tests in watch mode
pnpm test:coverage     # Generate coverage report
```

### E2E Testing

```bash
pnpm test:e2e          # Run end-to-end tests
```

## 🗄️ Database Management

### Schema Changes

1. **Modify the Prisma schema**
   ```bash
   # Edit apps/api/prisma/schema.prisma
   ```

2. **Generate migration**
   ```bash
   pnpm db:migrate
   ```

3. **Apply to development database**
   ```bash
   pnpm db:push
   ```

### Seeding Data

```bash
pnpm db:seed           # Seed with test data
```

## 🔧 Configuration

### Environment Variables

#### Backend (.env)
- `DATABASE_URL`: PostgreSQL connection string
- `REDIS_URL`: Redis connection string
- `JWT_SECRET`: JWT signing secret
- `CHUTES_AI_API_KEY`: Chutes AI API key

#### Frontend (apps/web/.env)
- `VITE_API_URL`: Backend API URL
- `VITE_WS_URL`: WebSocket URL

### AI Integration

The application integrates with multiple AI providers:

1. **Chutes AI** (Primary)
   - Model: deepseek-ai/DeepSeek-R1
   - Configuration in `apps/api/src/config/config.ts`

2. **OpenAI** (Fallback)
   - Optional integration
   - Requires `OPENAI_API_KEY`

## 📦 Package Management

### Adding Dependencies

```bash
# Add to specific workspace
pnpm add <package> --filter @waatch/web
pnpm add <package> --filter @waatch/api

# Add to shared package
pnpm add <package> --filter @waatch/shared

# Add to root (dev dependencies)
pnpm add -D <package> -w
```

### Workspace Dependencies

```bash
# Reference workspace package
pnpm add @waatch/shared --filter @waatch/web
```

## 🚀 Deployment

### Development Deployment

```bash
pnpm build             # Build all applications
pnpm start             # Start production servers
```

### Docker Deployment

```bash
# Build production images
docker-compose -f docker-compose.prod.yml build

# Start production stack
docker-compose -f docker-compose.prod.yml up -d
```

## 🐛 Debugging

### Backend Debugging

1. **Enable debug logging**
   ```bash
   LOG_LEVEL=debug pnpm dev
   ```

2. **Use VS Code debugger**
   - Set breakpoints in TypeScript files
   - Use the provided launch configuration

### Frontend Debugging

1. **React Developer Tools**
   - Install browser extension
   - Inspect component state and props

2. **Redux DevTools** (for Zustand)
   - Enable in development mode
   - Monitor state changes

## 📚 Additional Resources

- [API Documentation](http://localhost:3001/api-docs)
- [Prisma Documentation](https://www.prisma.io/docs)
- [React Documentation](https://react.dev)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [Zustand Documentation](https://github.com/pmndrs/zustand)

## 🆘 Troubleshooting

### Common Issues

1. **Port conflicts**
   ```bash
   # Kill processes on ports
   npx kill-port 3000 3001
   ```

2. **Database connection issues**
   ```bash
   # Restart Docker containers
   pnpm docker:down && pnpm docker:dev
   ```

3. **TypeScript errors**
   ```bash
   # Regenerate types
   pnpm db:generate
   pnpm type-check
   ```

4. **Package installation issues**
   ```bash
   # Clear cache and reinstall
   pnpm store prune
   rm -rf node_modules
   pnpm install
   ```

### Getting Help

- Check the [GitHub Issues](https://github.com/waatch/waatch/issues)
- Join our [Discord Community](https://discord.gg/waatch)
- Email: <EMAIL>
