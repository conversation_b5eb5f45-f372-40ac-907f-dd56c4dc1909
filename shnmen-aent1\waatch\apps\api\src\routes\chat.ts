import { Router } from 'express';
import { ChatRequestSchema, AgentSwitchRequestSchema } from '@waatch/shared';
import { asyncHandler } from '../middleware/errorHandler';
import { authenticateToken } from '../middleware/auth';
import { aiRateLimiter } from '../middleware/rateLimiter';
import { ChatService } from '../services/chat';
import { logger } from '../utils/logger';

const router = Router();
const chatService = new ChatService();

// Apply authentication to all chat routes
router.use(authenticateToken);

/**
 * @swagger
 * /api/chat/sessions:
 *   get:
 *     summary: Get user's chat sessions
 *     tags: [Chat]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *         description: Number of sessions to return
 *       - in: query
 *         name: offset
 *         schema:
 *           type: integer
 *           default: 0
 *         description: Number of sessions to skip
 *     responses:
 *       200:
 *         description: List of chat sessions
 */
router.get('/sessions', asyncHandler(async (req, res) => {
  const userId = req.user!.id;
  const limit = parseInt(req.query.limit as string) || 20;
  const offset = parseInt(req.query.offset as string) || 0;

  const sessions = await chatService.getUserSessions(userId, limit, offset);
  
  logger.info('Chat sessions requested', {
    userId,
    sessionCount: sessions.length,
    limit,
    offset,
  });
  
  res.json({ sessions });
}));

/**
 * @swagger
 * /api/chat/sessions:
 *   post:
 *     summary: Create new chat session
 *     tags: [Chat]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               agentId:
 *                 type: string
 *               title:
 *                 type: string
 *               context:
 *                 type: object
 *     responses:
 *       201:
 *         description: Chat session created
 */
router.post('/sessions', asyncHandler(async (req, res) => {
  const userId = req.user!.id;
  const { agentId, title, context } = req.body;

  const session = await chatService.createSession(userId, agentId, title, context);
  
  logger.info('Chat session created', {
    userId,
    sessionId: session.id,
    agentId,
  });
  
  res.status(201).json({ session });
}));

/**
 * @swagger
 * /api/chat/sessions/{sessionId}:
 *   get:
 *     summary: Get chat session details
 *     tags: [Chat]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: sessionId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Chat session details
 *       404:
 *         description: Session not found
 */
router.get('/sessions/:sessionId', asyncHandler(async (req, res) => {
  const userId = req.user!.id;
  const { sessionId } = req.params;

  const session = await chatService.getSession(sessionId, userId);
  
  res.json({ session });
}));

/**
 * @swagger
 * /api/chat/sessions/{sessionId}/messages:
 *   get:
 *     summary: Get messages for a chat session
 *     tags: [Chat]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: sessionId
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 50
 *       - in: query
 *         name: before
 *         schema:
 *           type: string
 *         description: Message ID to get messages before
 *     responses:
 *       200:
 *         description: List of messages
 */
router.get('/sessions/:sessionId/messages', asyncHandler(async (req, res) => {
  const userId = req.user!.id;
  const { sessionId } = req.params;
  const limit = parseInt(req.query.limit as string) || 50;
  const before = req.query.before as string;

  const messages = await chatService.getMessages(sessionId, userId, limit, before);
  
  res.json({ messages });
}));

/**
 * @swagger
 * /api/chat/sessions/{sessionId}/messages:
 *   post:
 *     summary: Send message to chat session
 *     tags: [Chat]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: sessionId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - message
 *               - agentId
 *             properties:
 *               message:
 *                 type: string
 *               agentId:
 *                 type: string
 *               context:
 *                 type: object
 *     responses:
 *       200:
 *         description: Message sent and response generated
 *       429:
 *         description: Rate limit exceeded
 */
router.post('/sessions/:sessionId/messages', aiRateLimiter, asyncHandler(async (req, res) => {
  const userId = req.user!.id;
  const { sessionId } = req.params;
  
  const chatRequest = ChatRequestSchema.parse({
    ...req.body,
    sessionId,
  });

  const response = await chatService.sendMessage(userId, chatRequest);
  
  logger.info('Message sent', {
    userId,
    sessionId,
    agentId: chatRequest.agentId,
    messageLength: chatRequest.message.length,
  });
  
  res.json(response);
}));

/**
 * @swagger
 * /api/chat/sessions/{sessionId}/switch-agent:
 *   post:
 *     summary: Switch agent in chat session
 *     tags: [Chat]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: sessionId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - newAgentId
 *             properties:
 *               newAgentId:
 *                 type: string
 *               preserveContext:
 *                 type: boolean
 *                 default: true
 *     responses:
 *       200:
 *         description: Agent switched successfully
 */
router.post('/sessions/:sessionId/switch-agent', asyncHandler(async (req, res) => {
  const userId = req.user!.id;
  const { sessionId } = req.params;
  
  const switchRequest = AgentSwitchRequestSchema.parse({
    sessionId,
    ...req.body,
  });

  const result = await chatService.switchAgent(userId, switchRequest);
  
  logger.info('Agent switched', {
    userId,
    sessionId,
    newAgentId: switchRequest.newAgentId,
    preserveContext: switchRequest.preserveContext,
  });
  
  res.json(result);
}));

/**
 * @swagger
 * /api/chat/sessions/{sessionId}:
 *   delete:
 *     summary: Delete chat session
 *     tags: [Chat]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: sessionId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Session deleted successfully
 */
router.delete('/sessions/:sessionId', asyncHandler(async (req, res) => {
  const userId = req.user!.id;
  const { sessionId } = req.params;

  await chatService.deleteSession(sessionId, userId);
  
  logger.info('Chat session deleted', {
    userId,
    sessionId,
  });
  
  res.json({ message: 'Session deleted successfully' });
}));

export { router as chatRoutes };
