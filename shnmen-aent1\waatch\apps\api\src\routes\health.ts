import { Router } from 'express';
import { asyncHandler } from '../middleware/errorHandler';
import { checkDatabaseHealth } from '../services/database';
import { checkRedisHealth } from '../services/redis';
import { logger } from '../utils/logger';

const router = Router();

/**
 * @swagger
 * /api/health:
 *   get:
 *     summary: Health check endpoint
 *     tags: [Health]
 *     responses:
 *       200:
 *         description: Service is healthy
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: healthy
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 *                 services:
 *                   type: object
 *                   properties:
 *                     database:
 *                       type: string
 *                       example: healthy
 *                     redis:
 *                       type: string
 *                       example: healthy
 *       503:
 *         description: Service is unhealthy
 */
router.get('/', asyncHandler(async (req, res) => {
  const startTime = Date.now();
  
  // Check all services
  const [databaseHealthy, redisHealthy] = await Promise.all([
    checkDatabaseHealth(),
    checkRedisHealth(),
  ]);

  const responseTime = Date.now() - startTime;
  const allHealthy = databaseHealthy && redisHealthy;

  const healthStatus = {
    status: allHealthy ? 'healthy' : 'unhealthy',
    timestamp: new Date().toISOString(),
    responseTime: `${responseTime}ms`,
    services: {
      database: databaseHealthy ? 'healthy' : 'unhealthy',
      redis: redisHealthy ? 'healthy' : 'unhealthy',
    },
    version: process.env.npm_package_version || '1.0.0',
    environment: process.env.NODE_ENV || 'development',
  };

  // Log health check
  logger.info('Health check performed', {
    status: healthStatus.status,
    responseTime,
    services: healthStatus.services,
  });

  res.status(allHealthy ? 200 : 503).json(healthStatus);
}));

/**
 * @swagger
 * /api/health/ready:
 *   get:
 *     summary: Readiness check endpoint
 *     tags: [Health]
 *     responses:
 *       200:
 *         description: Service is ready
 *       503:
 *         description: Service is not ready
 */
router.get('/ready', asyncHandler(async (req, res) => {
  // More comprehensive readiness check
  const checks = await Promise.allSettled([
    checkDatabaseHealth(),
    checkRedisHealth(),
  ]);

  const allReady = checks.every(check => 
    check.status === 'fulfilled' && check.value === true
  );

  if (allReady) {
    res.status(200).json({ status: 'ready' });
  } else {
    res.status(503).json({ status: 'not ready' });
  }
}));

/**
 * @swagger
 * /api/health/live:
 *   get:
 *     summary: Liveness check endpoint
 *     tags: [Health]
 *     responses:
 *       200:
 *         description: Service is alive
 */
router.get('/live', asyncHandler(async (req, res) => {
  // Simple liveness check - just return 200 if server is running
  res.status(200).json({ 
    status: 'alive',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
  });
}));

export { router as healthRoutes };
