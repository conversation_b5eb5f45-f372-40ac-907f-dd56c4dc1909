import { Server as SocketIOServer, Socket } from 'socket.io';
import jwt from 'jsonwebtoken';
import { config } from '../config/config';
import { prisma } from './database';
import { logger } from '../utils/logger';
import { SocketEvent } from '@waatch/shared';

interface AuthenticatedSocket extends Socket {
  userId?: string;
  user?: {
    id: string;
    email: string;
  };
}

export function initializeSocket(io: SocketIOServer): void {
  // Authentication middleware
  io.use(async (socket: AuthenticatedSocket, next) => {
    try {
      const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.split(' ')[1];
      
      if (!token) {
        return next(new Error('Authentication token required'));
      }

      const payload = jwt.verify(token, config.jwt.secret) as any;
      
      if (payload.type !== 'access') {
        return next(new Error('Invalid token type'));
      }

      // Get user from database
      const user = await prisma.user.findUnique({
        where: { id: payload.userId },
        select: { id: true, email: true, isActive: true },
      });

      if (!user || !user.isActive) {
        return next(new Error('User not found or inactive'));
      }

      socket.userId = user.id;
      socket.user = user;
      
      logger.info('Socket authenticated', {
        socketId: socket.id,
        userId: user.id,
        email: user.email,
      });

      next();
    } catch (error) {
      logger.error('Socket authentication failed', {
        socketId: socket.id,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      next(new Error('Authentication failed'));
    }
  });

  // Connection handler
  io.on('connection', (socket: AuthenticatedSocket) => {
    logger.info('Socket connected', {
      socketId: socket.id,
      userId: socket.userId,
    });

    // Join user-specific room
    if (socket.userId) {
      socket.join(`user:${socket.userId}`);
    }

    // Handle joining chat session rooms
    socket.on('join-session', async (sessionId: string) => {
      try {
        // Verify user owns the session
        const session = await prisma.chatSession.findFirst({
          where: {
            id: sessionId,
            userId: socket.userId,
          },
        });

        if (!session) {
          socket.emit('error', {
            type: 'error',
            data: {
              sessionId,
              error: 'Session not found or access denied',
              code: 'SESSION_ACCESS_DENIED',
            },
          } as SocketEvent);
          return;
        }

        socket.join(`session:${sessionId}`);
        
        logger.info('Socket joined session', {
          socketId: socket.id,
          userId: socket.userId,
          sessionId,
        });

        socket.emit('session-joined', { sessionId });
      } catch (error) {
        logger.error('Failed to join session', {
          socketId: socket.id,
          userId: socket.userId,
          sessionId,
          error: error instanceof Error ? error.message : 'Unknown error',
        });

        socket.emit('error', {
          type: 'error',
          data: {
            sessionId,
            error: 'Failed to join session',
            code: 'SESSION_JOIN_FAILED',
          },
        } as SocketEvent);
      }
    });

    // Handle leaving chat session rooms
    socket.on('leave-session', (sessionId: string) => {
      socket.leave(`session:${sessionId}`);
      
      logger.info('Socket left session', {
        socketId: socket.id,
        userId: socket.userId,
        sessionId,
      });

      socket.emit('session-left', { sessionId });
    });

    // Handle typing indicators
    socket.on('typing-start', (sessionId: string) => {
      socket.to(`session:${sessionId}`).emit('user-typing-start', {
        userId: socket.userId,
        sessionId,
      });
    });

    socket.on('typing-stop', (sessionId: string) => {
      socket.to(`session:${sessionId}`).emit('user-typing-stop', {
        userId: socket.userId,
        sessionId,
      });
    });

    // Handle disconnection
    socket.on('disconnect', (reason) => {
      logger.info('Socket disconnected', {
        socketId: socket.id,
        userId: socket.userId,
        reason,
      });
    });

    // Handle errors
    socket.on('error', (error) => {
      logger.error('Socket error', {
        socketId: socket.id,
        userId: socket.userId,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    });
  });

  // Store io instance for use in other services
  global.io = io;
}

// Helper functions to emit events from other services
export class SocketService {
  private io: SocketIOServer;

  constructor(io: SocketIOServer) {
    this.io = io;
  }

  // Emit message start event
  emitMessageStart(sessionId: string, messageId: string): void {
    this.io.to(`session:${sessionId}`).emit('message:start', {
      type: 'message:start',
      data: { sessionId, messageId },
    } as SocketEvent);
  }

  // Emit message chunk event
  emitMessageChunk(sessionId: string, messageId: string, chunk: string): void {
    this.io.to(`session:${sessionId}`).emit('message:chunk', {
      type: 'message:chunk',
      data: { sessionId, messageId, chunk },
    } as SocketEvent);
  }

  // Emit message end event
  emitMessageEnd(sessionId: string, messageId: string, message: any): void {
    this.io.to(`session:${sessionId}`).emit('message:end', {
      type: 'message:end',
      data: { sessionId, messageId, message },
    } as SocketEvent);
  }

  // Emit agent switch event
  emitAgentSwitch(sessionId: string, fromAgent: string, toAgent: string): void {
    this.io.to(`session:${sessionId}`).emit('agent:switch', {
      type: 'agent:switch',
      data: { sessionId, fromAgent, toAgent },
    } as SocketEvent);
  }

  // Emit typing start event
  emitTypingStart(sessionId: string, agentId: string): void {
    this.io.to(`session:${sessionId}`).emit('typing:start', {
      type: 'typing:start',
      data: { sessionId, agentId },
    } as SocketEvent);
  }

  // Emit typing stop event
  emitTypingStop(sessionId: string, agentId: string): void {
    this.io.to(`session:${sessionId}`).emit('typing:stop', {
      type: 'typing:stop',
      data: { sessionId, agentId },
    } as SocketEvent);
  }

  // Emit error event
  emitError(sessionId: string, error: string, code?: string): void {
    this.io.to(`session:${sessionId}`).emit('error', {
      type: 'error',
      data: { sessionId, error, code },
    } as SocketEvent);
  }

  // Send notification to specific user
  sendUserNotification(userId: string, notification: any): void {
    this.io.to(`user:${userId}`).emit('notification', notification);
  }
}

// Global socket service instance
declare global {
  var io: SocketIOServer | undefined;
  var socketService: SocketService | undefined;
}

export function getSocketService(): SocketService | undefined {
  if (global.io && !global.socketService) {
    global.socketService = new SocketService(global.io);
  }
  return global.socketService;
}
