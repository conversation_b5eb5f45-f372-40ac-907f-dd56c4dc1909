{"extends": "@waatch/config/typescript/react.json", "compilerOptions": {"baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/components/*": ["./src/components/*"], "@/hooks/*": ["./src/hooks/*"], "@/stores/*": ["./src/stores/*"], "@/utils/*": ["./src/utils/*"], "@/types/*": ["./src/types/*"], "@/services/*": ["./src/services/*"]}}, "include": ["src/**/*", "vite.config.ts"], "exclude": ["node_modules", "dist"]}