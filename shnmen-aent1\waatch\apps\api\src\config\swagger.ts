import { Express } from 'express';
import swaggerJsdoc from 'swagger-jsdoc';
import swaggerUi from 'swagger-ui-express';
import { config } from './config';

const options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'Waatch API',
      version: '1.0.0',
      description: 'AI-Powered Multi-Agent Code Generation Platform API',
      contact: {
        name: 'Waatch Team',
        email: '<EMAIL>',
        url: 'https://waatch.dev',
      },
      license: {
        name: 'MIT',
        url: 'https://opensource.org/licenses/MIT',
      },
    },
    servers: [
      {
        url: `http://localhost:${config.port}`,
        description: 'Development server',
      },
      {
        url: 'https://api.waatch.dev',
        description: 'Production server',
      },
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          description: 'JWT token for authentication',
        },
      },
      schemas: {
        Error: {
          type: 'object',
          properties: {
            error: {
              type: 'object',
              properties: {
                message: {
                  type: 'string',
                  description: 'Error message',
                },
                code: {
                  type: 'string',
                  description: 'Error code',
                },
                details: {
                  type: 'object',
                  description: 'Additional error details',
                },
                timestamp: {
                  type: 'string',
                  format: 'date-time',
                  description: 'Error timestamp',
                },
                requestId: {
                  type: 'string',
                  description: 'Request ID for tracking',
                },
              },
              required: ['message', 'code', 'timestamp'],
            },
          },
        },
        User: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              description: 'User ID',
            },
            email: {
              type: 'string',
              format: 'email',
              description: 'User email',
            },
            firstName: {
              type: 'string',
              description: 'User first name',
            },
            lastName: {
              type: 'string',
              description: 'User last name',
            },
            avatarUrl: {
              type: 'string',
              format: 'uri',
              description: 'User avatar URL',
            },
            preferences: {
              type: 'object',
              description: 'User preferences',
            },
            createdAt: {
              type: 'string',
              format: 'date-time',
              description: 'Account creation date',
            },
            updatedAt: {
              type: 'string',
              format: 'date-time',
              description: 'Last update date',
            },
            lastLogin: {
              type: 'string',
              format: 'date-time',
              description: 'Last login date',
            },
            isActive: {
              type: 'boolean',
              description: 'Account status',
            },
          },
          required: ['id', 'email', 'isActive'],
        },
        Agent: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              description: 'Agent ID',
            },
            name: {
              type: 'string',
              description: 'Agent name',
            },
            description: {
              type: 'string',
              description: 'Agent description',
            },
            capabilities: {
              type: 'array',
              items: {
                type: 'string',
              },
              description: 'Agent capabilities',
            },
            tools: {
              type: 'array',
              items: {
                type: 'object',
              },
              description: 'Available tools',
            },
            modelConfig: {
              type: 'object',
              description: 'AI model configuration',
            },
            version: {
              type: 'string',
              description: 'Agent version',
            },
            isActive: {
              type: 'boolean',
              description: 'Agent status',
            },
          },
          required: ['id', 'name', 'description', 'capabilities', 'isActive'],
        },
        ChatSession: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              description: 'Session ID',
            },
            userId: {
              type: 'string',
              description: 'User ID',
            },
            agentId: {
              type: 'string',
              description: 'Current agent ID',
            },
            title: {
              type: 'string',
              description: 'Session title',
            },
            context: {
              type: 'object',
              description: 'Session context',
            },
            workspaceState: {
              type: 'object',
              description: 'Workspace state',
            },
            createdAt: {
              type: 'string',
              format: 'date-time',
              description: 'Creation date',
            },
            updatedAt: {
              type: 'string',
              format: 'date-time',
              description: 'Last update date',
            },
            lastActivity: {
              type: 'string',
              format: 'date-time',
              description: 'Last activity date',
            },
            isArchived: {
              type: 'boolean',
              description: 'Archive status',
            },
          },
          required: ['id', 'userId', 'agentId', 'createdAt', 'lastActivity'],
        },
        Message: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              description: 'Message ID',
            },
            sessionId: {
              type: 'string',
              description: 'Session ID',
            },
            role: {
              type: 'string',
              enum: ['user', 'assistant', 'system'],
              description: 'Message role',
            },
            content: {
              type: 'string',
              description: 'Message content',
            },
            metadata: {
              type: 'object',
              description: 'Message metadata',
            },
            toolCalls: {
              type: 'array',
              items: {
                type: 'object',
              },
              description: 'Tool calls made',
            },
            toolResults: {
              type: 'array',
              items: {
                type: 'object',
              },
              description: 'Tool execution results',
            },
            tokensUsed: {
              type: 'integer',
              description: 'Tokens consumed',
            },
            processingTimeMs: {
              type: 'integer',
              description: 'Processing time in milliseconds',
            },
            createdAt: {
              type: 'string',
              format: 'date-time',
              description: 'Creation date',
            },
          },
          required: ['id', 'sessionId', 'role', 'content', 'createdAt'],
        },
      },
    },
    tags: [
      {
        name: 'Authentication',
        description: 'User authentication and authorization',
      },
      {
        name: 'Agents',
        description: 'AI agent management and interaction',
      },
      {
        name: 'Chat',
        description: 'Chat sessions and messaging',
      },
      {
        name: 'Projects',
        description: 'Project management and code generation',
      },
      {
        name: 'Health',
        description: 'Service health and monitoring',
      },
    ],
  },
  apis: ['./src/routes/*.ts'], // Path to the API files
};

const specs = swaggerJsdoc(options);

export function setupSwagger(app: Express): void {
  // Swagger UI setup
  app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(specs, {
    explorer: true,
    customCss: '.swagger-ui .topbar { display: none }',
    customSiteTitle: 'Waatch API Documentation',
    swaggerOptions: {
      persistAuthorization: true,
      displayRequestDuration: true,
      docExpansion: 'none',
      filter: true,
      showExtensions: true,
      showCommonExtensions: true,
    },
  }));

  // JSON endpoint for the OpenAPI spec
  app.get('/api-docs.json', (req, res) => {
    res.setHeader('Content-Type', 'application/json');
    res.send(specs);
  });
}
