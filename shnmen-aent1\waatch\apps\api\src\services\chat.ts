import { 
  ChatRequest, 
  ChatResponse, 
  ChatSession, 
  Message, 
  AgentSwitchRequest,
  NotFoundError,
  AuthorizationError
} from '@waatch/shared';
import { prisma } from './database';
import { AIService } from './ai';
import { AgentService } from './agent';
import { logger } from '../utils/logger';

export class ChatService {
  private aiService: AIService;
  private agentService: AgentService;

  constructor() {
    this.aiService = new AIService();
    this.agentService = new AgentService();
  }

  // Get user's chat sessions
  async getUserSessions(userId: string, limit: number = 20, offset: number = 0): Promise<ChatSession[]> {
    const sessions = await prisma.chatSession.findMany({
      where: { 
        userId,
        isArchived: false,
      },
      orderBy: { lastActivity: 'desc' },
      take: limit,
      skip: offset,
      include: {
        _count: {
          select: { messages: true },
        },
      },
    });

    return sessions.map(session => ({
      id: session.id,
      userId: session.userId,
      agentId: session.agentId,
      title: session.title,
      context: session.context as Record<string, any>,
      workspaceState: session.workspaceState as Record<string, any>,
      createdAt: session.createdAt,
      updatedAt: session.updatedAt,
      lastActivity: session.lastActivity,
      isArchived: session.isArchived,
      messageCount: (session._count as any).messages,
    })) as ChatSession[];
  }

  // Create new chat session
  async createSession(
    userId: string, 
    agentId: string, 
    title?: string, 
    context?: Record<string, any>
  ): Promise<ChatSession> {
    // Verify agent exists
    await this.agentService.getAgentById(agentId);

    const session = await prisma.chatSession.create({
      data: {
        userId,
        agentId,
        title: title || `Chat with ${agentId}`,
        context: context || {},
        workspaceState: {},
      },
    });

    return {
      id: session.id,
      userId: session.userId,
      agentId: session.agentId,
      title: session.title,
      context: session.context as Record<string, any>,
      workspaceState: session.workspaceState as Record<string, any>,
      createdAt: session.createdAt,
      updatedAt: session.updatedAt,
      lastActivity: session.lastActivity,
      isArchived: session.isArchived,
    };
  }

  // Get chat session
  async getSession(sessionId: string, userId: string): Promise<ChatSession> {
    const session = await prisma.chatSession.findFirst({
      where: { 
        id: sessionId,
        userId,
      },
    });

    if (!session) {
      throw new NotFoundError('Chat session');
    }

    return {
      id: session.id,
      userId: session.userId,
      agentId: session.agentId,
      title: session.title,
      context: session.context as Record<string, any>,
      workspaceState: session.workspaceState as Record<string, any>,
      createdAt: session.createdAt,
      updatedAt: session.updatedAt,
      lastActivity: session.lastActivity,
      isArchived: session.isArchived,
    };
  }

  // Get messages for a session
  async getMessages(
    sessionId: string, 
    userId: string, 
    limit: number = 50, 
    before?: string
  ): Promise<Message[]> {
    // Verify user owns the session
    await this.getSession(sessionId, userId);

    const whereClause: any = { sessionId };
    if (before) {
      whereClause.createdAt = { lt: new Date(before) };
    }

    const messages = await prisma.message.findMany({
      where: whereClause,
      orderBy: { createdAt: 'desc' },
      take: limit,
    });

    return messages.map(message => ({
      id: message.id,
      sessionId: message.sessionId,
      role: message.role as 'user' | 'assistant' | 'system',
      content: message.content,
      metadata: message.metadata as Record<string, any>,
      toolCalls: message.toolCalls as any[],
      toolResults: message.toolResults as any[],
      tokensUsed: message.tokensUsed,
      processingTimeMs: message.processingTimeMs,
      createdAt: message.createdAt,
    })).reverse(); // Return in chronological order
  }

  // Send message and get AI response
  async sendMessage(userId: string, request: ChatRequest): Promise<ChatResponse> {
    const startTime = Date.now();

    // Verify session ownership
    const session = await this.getSession(request.sessionId!, userId);

    // Get agent configuration
    const agent = await this.agentService.getAgentById(request.agentId);

    // Store user message
    const userMessage = await prisma.message.create({
      data: {
        sessionId: request.sessionId!,
        role: 'user',
        content: request.message,
        metadata: request.context || {},
      },
    });

    try {
      // Generate AI response
      const aiResponse = await this.aiService.generateResponse(
        request.message,
        agent,
        {
          sessionId: request.sessionId!,
          userId,
          workspaceState: session.workspaceState,
          permissions: ['read:codebase', 'write:files'], // TODO: Get from user permissions
          agentConfig: agent,
        }
      );

      // Store AI response
      const assistantMessage = await prisma.message.create({
        data: {
          sessionId: request.sessionId!,
          role: 'assistant',
          content: aiResponse.content,
          metadata: aiResponse.metadata || {},
          toolCalls: aiResponse.toolCalls || [],
          tokensUsed: aiResponse.metadata?.tokensUsed,
          processingTimeMs: Date.now() - startTime,
        },
      });

      // Update session activity
      await prisma.chatSession.update({
        where: { id: request.sessionId! },
        data: { 
          lastActivity: new Date(),
          context: {
            ...session.context,
            ...request.context,
          },
        },
      });

      // Log usage analytics
      await this.logAgentUsage(
        userId,
        request.agentId,
        request.sessionId!,
        'chat_message',
        true,
        Date.now() - startTime,
        aiResponse.metadata?.tokensUsed
      );

      return {
        message: {
          id: assistantMessage.id,
          sessionId: assistantMessage.sessionId,
          role: assistantMessage.role as 'assistant',
          content: assistantMessage.content,
          metadata: assistantMessage.metadata as Record<string, any>,
          toolCalls: assistantMessage.toolCalls as any[],
          toolResults: assistantMessage.toolResults as any[],
          tokensUsed: assistantMessage.tokensUsed,
          processingTimeMs: assistantMessage.processingTimeMs,
          createdAt: assistantMessage.createdAt,
        },
        session: {
          ...session,
          lastActivity: new Date(),
        },
      };

    } catch (error) {
      // Log failed usage
      await this.logAgentUsage(
        userId,
        request.agentId,
        request.sessionId!,
        'chat_message',
        false,
        Date.now() - startTime,
        0,
        error instanceof Error ? error.message : 'Unknown error'
      );

      throw error;
    }
  }

  // Switch agent in session
  async switchAgent(userId: string, request: AgentSwitchRequest): Promise<{
    session: ChatSession;
    message: string;
  }> {
    // Verify session ownership
    const session = await this.getSession(request.sessionId, userId);

    // Verify new agent exists
    const newAgent = await this.agentService.getAgentById(request.newAgentId);

    // Update session
    const updatedSession = await prisma.chatSession.update({
      where: { id: request.sessionId },
      data: {
        agentId: request.newAgentId,
        lastActivity: new Date(),
        context: request.preserveContext ? session.context : {},
      },
    });

    // Add system message about agent switch
    await prisma.message.create({
      data: {
        sessionId: request.sessionId,
        role: 'system',
        content: `Switched to ${newAgent.name}${request.preserveContext ? ' (context preserved)' : ' (fresh start)'}`,
        metadata: {
          agentSwitch: true,
          fromAgent: session.agentId,
          toAgent: request.newAgentId,
          preserveContext: request.preserveContext,
        },
      },
    });

    return {
      session: {
        ...session,
        agentId: request.newAgentId,
        lastActivity: new Date(),
        context: request.preserveContext ? session.context : {},
      },
      message: `Successfully switched to ${newAgent.name}`,
    };
  }

  // Delete chat session
  async deleteSession(sessionId: string, userId: string): Promise<void> {
    // Verify session ownership
    await this.getSession(sessionId, userId);

    // Delete session and all related messages (cascade)
    await prisma.chatSession.delete({
      where: { id: sessionId },
    });
  }

  // Log agent usage for analytics
  private async logAgentUsage(
    userId: string,
    agentId: string,
    sessionId: string,
    actionType: string,
    success: boolean,
    executionTimeMs: number,
    tokensConsumed?: number,
    errorMessage?: string
  ): Promise<void> {
    try {
      await prisma.agentUsage.create({
        data: {
          userId,
          agentId,
          sessionId,
          actionType,
          success,
          executionTimeMs,
          tokensConsumed,
          errorMessage,
        },
      });
    } catch (error) {
      logger.error('Failed to log agent usage', { error });
    }
  }
}
