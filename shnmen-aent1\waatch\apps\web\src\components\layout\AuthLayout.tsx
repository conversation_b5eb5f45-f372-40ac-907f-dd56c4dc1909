import { ReactNode } from 'react';

interface AuthLayoutProps {
  children: ReactNode;
}

export function AuthLayout({ children }: AuthLayoutProps) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      <div className="flex min-h-screen">
        {/* Left side - Branding */}
        <div className="hidden lg:flex lg:w-1/2 lg:flex-col lg:justify-center lg:px-12">
          <div className="mx-auto max-w-md">
            <div className="mb-8">
              <h1 className="text-4xl font-bold text-gray-900 dark:text-white">
                Waatch
              </h1>
              <p className="mt-2 text-lg text-gray-600 dark:text-gray-300">
                AI-Powered Multi-Agent Code Generation Platform
              </p>
            </div>
            
            <div className="space-y-6">
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0">
                  <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900">
                    <span className="text-sm font-medium text-blue-600 dark:text-blue-300">1</span>
                  </div>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-gray-900 dark:text-white">
                    12 Specialized AI Agents
                  </h3>
                  <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                    Each agent brings unique capabilities for different development tasks
                  </p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0">
                  <div className="flex h-8 w-8 items-center justify-center rounded-full bg-green-100 dark:bg-green-900">
                    <span className="text-sm font-medium text-green-600 dark:text-green-300">2</span>
                  </div>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-gray-900 dark:text-white">
                    Real-time Collaboration
                  </h3>
                  <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                    Switch between agents seamlessly while preserving context
                  </p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0">
                  <div className="flex h-8 w-8 items-center justify-center rounded-full bg-purple-100 dark:bg-purple-900">
                    <span className="text-sm font-medium text-purple-600 dark:text-purple-300">3</span>
                  </div>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-gray-900 dark:text-white">
                    Complete Project Management
                  </h3>
                  <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                    From idea to deployment with integrated tools and workflows
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Right side - Auth form */}
        <div className="flex w-full flex-col justify-center px-6 py-12 lg:w-1/2 lg:px-8">
          <div className="mx-auto w-full max-w-sm">
            <div className="mb-8 lg:hidden">
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                Waatch
              </h1>
              <p className="mt-2 text-gray-600 dark:text-gray-300">
                AI-Powered Development Platform
              </p>
            </div>
            
            {children}
          </div>
        </div>
      </div>
    </div>
  );
}
