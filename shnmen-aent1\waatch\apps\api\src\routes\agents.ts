import { Router } from 'express';
import { asyncHand<PERSON> } from '../middleware/errorHandler';
import { authenticateToken } from '../middleware/auth';
import { AgentService } from '../services/agent';
import { logger } from '../utils/logger';

const router = Router();
const agentService = new AgentService();

// Apply authentication to all agent routes
router.use(authenticateToken);

/**
 * @swagger
 * /api/agents:
 *   get:
 *     summary: Get all available agents
 *     tags: [Agents]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of available agents
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 agents:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                       name:
 *                         type: string
 *                       description:
 *                         type: string
 *                       capabilities:
 *                         type: array
 *                         items:
 *                           type: string
 *                       isActive:
 *                         type: boolean
 */
router.get('/', asyncHandler(async (req, res) => {
  const agents = await agentService.getAllAgents();
  
  logger.info('Agents list requested', {
    userId: req.user!.id,
    agentCount: agents.length,
  });
  
  res.json({ agents });
}));

/**
 * @swagger
 * /api/agents/{agentId}:
 *   get:
 *     summary: Get specific agent details
 *     tags: [Agents]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: agentId
 *         required: true
 *         schema:
 *           type: string
 *         description: Agent ID
 *     responses:
 *       200:
 *         description: Agent details
 *       404:
 *         description: Agent not found
 */
router.get('/:agentId', asyncHandler(async (req, res) => {
  const { agentId } = req.params;
  const agent = await agentService.getAgentById(agentId);
  
  logger.info('Agent details requested', {
    userId: req.user!.id,
    agentId,
  });
  
  res.json({ agent });
}));

/**
 * @swagger
 * /api/agents/{agentId}/capabilities:
 *   get:
 *     summary: Get agent capabilities and tools
 *     tags: [Agents]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: agentId
 *         required: true
 *         schema:
 *           type: string
 *         description: Agent ID
 *     responses:
 *       200:
 *         description: Agent capabilities and tools
 */
router.get('/:agentId/capabilities', asyncHandler(async (req, res) => {
  const { agentId } = req.params;
  const capabilities = await agentService.getAgentCapabilities(agentId);
  
  res.json({ capabilities });
}));

/**
 * @swagger
 * /api/agents/recommend:
 *   post:
 *     summary: Get agent recommendations based on task
 *     tags: [Agents]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               task:
 *                 type: string
 *                 description: Description of the task
 *               language:
 *                 type: string
 *                 description: Programming language (optional)
 *               framework:
 *                 type: string
 *                 description: Framework (optional)
 *               complexity:
 *                 type: string
 *                 enum: [low, medium, high]
 *                 description: Task complexity (optional)
 *     responses:
 *       200:
 *         description: Recommended agents
 */
router.post('/recommend', asyncHandler(async (req, res) => {
  const criteria = req.body;
  const recommendations = await agentService.recommendAgents(criteria);
  
  logger.info('Agent recommendations requested', {
    userId: req.user!.id,
    criteria,
    recommendationCount: recommendations.length,
  });
  
  res.json({ recommendations });
}));

export { router as agentRoutes };
