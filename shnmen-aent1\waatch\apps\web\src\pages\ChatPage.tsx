import { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { MessageSquare, Plus, Send } from 'lucide-react';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';

export function ChatPage() {
  const { sessionId } = useParams();
  const [isLoading, setIsLoading] = useState(true);
  const [message, setMessage] = useState('');

  useEffect(() => {
    // TODO: Load chat session if sessionId is provided
    // TODO: Load available agents
    setIsLoading(false);
  }, [sessionId]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="flex h-full">
      {/* Sidebar - Chat Sessions */}
      <div className="w-80 border-r border-border bg-card">
        <div className="p-4 border-b border-border">
          <div className="flex items-center justify-between">
            <h2 className="font-semibold text-foreground">Chat Sessions</h2>
            <button className="p-2 hover:bg-accent rounded-md">
              <Plus className="h-4 w-4" />
            </button>
          </div>
        </div>
        
        <div className="p-4">
          <div className="space-y-2">
            <div className="p-3 bg-primary/10 rounded-lg border border-primary/20">
              <div className="flex items-center space-x-2 mb-1">
                <div className="h-2 w-2 bg-green-500 rounded-full"></div>
                <span className="text-sm font-medium">Devin Engineer</span>
              </div>
              <p className="text-xs text-muted-foreground truncate">
                Help me build a React component...
              </p>
              <p className="text-xs text-muted-foreground mt-1">2 hours ago</p>
            </div>
            
            <div className="p-3 hover:bg-accent rounded-lg cursor-pointer">
              <div className="flex items-center space-x-2 mb-1">
                <div className="h-2 w-2 bg-gray-400 rounded-full"></div>
                <span className="text-sm font-medium">DataForge</span>
              </div>
              <p className="text-xs text-muted-foreground truncate">
                Design a database schema for...
              </p>
              <p className="text-xs text-muted-foreground mt-1">1 day ago</p>
            </div>
          </div>
        </div>
      </div>

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col">
        {sessionId ? (
          <>
            {/* Chat Header */}
            <div className="p-4 border-b border-border bg-card">
              <div className="flex items-center space-x-3">
                <div className="h-8 w-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                  <span className="text-white text-xs font-bold">DE</span>
                </div>
                <div>
                  <h3 className="font-medium text-foreground">Devin Engineer</h3>
                  <p className="text-xs text-muted-foreground">Autonomous software development</p>
                </div>
                <div className="h-2 w-2 bg-green-500 rounded-full ml-auto"></div>
              </div>
            </div>

            {/* Messages */}
            <div className="flex-1 overflow-auto p-4 space-y-4">
              <div className="flex justify-start">
                <div className="max-w-3xl bg-muted rounded-lg p-4">
                  <p className="text-sm text-foreground">
                    Hello! I'm Devin, your autonomous software development agent. I can help you build complete applications, 
                    write code, debug issues, and implement features. What would you like to work on today?
                  </p>
                </div>
              </div>
              
              <div className="flex justify-end">
                <div className="max-w-3xl bg-primary rounded-lg p-4">
                  <p className="text-sm text-primary-foreground">
                    I need help building a React component for a user profile card. It should display the user's avatar, 
                    name, email, and a few action buttons.
                  </p>
                </div>
              </div>
              
              <div className="flex justify-start">
                <div className="max-w-3xl bg-muted rounded-lg p-4">
                  <p className="text-sm text-foreground mb-3">
                    I'll help you create a user profile card component. Let me build a clean, reusable React component with TypeScript:
                  </p>
                  <div className="bg-gray-900 rounded-md p-4 text-sm font-mono text-gray-100 overflow-x-auto">
                    <pre>{`interface UserProfileCardProps {
  user: {
    id: string;
    name: string;
    email: string;
    avatar?: string;
  };
  onEdit?: () => void;
  onMessage?: () => void;
}

export function UserProfileCard({ user, onEdit, onMessage }: UserProfileCardProps) {
  return (
    <div className="bg-white rounded-lg shadow-md p-6 max-w-sm">
      <div className="flex flex-col items-center">
        <img
          src={user.avatar || '/default-avatar.png'}
          alt={user.name}
          className="w-20 h-20 rounded-full mb-4"
        />
        <h3 className="text-lg font-semibold text-gray-900">{user.name}</h3>
        <p className="text-gray-600 mb-4">{user.email}</p>
        
        <div className="flex space-x-2">
          <button
            onClick={onEdit}
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
          >
            Edit Profile
          </button>
          <button
            onClick={onMessage}
            className="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600"
          >
            Send Message
          </button>
        </div>
      </div>
    </div>
  );
}`}</pre>
                  </div>
                </div>
              </div>
            </div>

            {/* Message Input */}
            <div className="p-4 border-t border-border bg-card">
              <div className="flex space-x-2">
                <input
                  type="text"
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  placeholder="Type your message..."
                  className="flex-1 px-3 py-2 border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary"
                  onKeyPress={(e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault();
                      // TODO: Send message
                      setMessage('');
                    }
                  }}
                />
                <button className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 flex items-center space-x-2">
                  <Send className="h-4 w-4" />
                  <span>Send</span>
                </button>
              </div>
            </div>
          </>
        ) : (
          /* No Session Selected */
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <MessageSquare className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium text-foreground mb-2">Start a conversation</h3>
              <p className="text-muted-foreground mb-4">
                Select a chat session or create a new one to start chatting with AI agents
              </p>
              <button className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 flex items-center space-x-2 mx-auto">
                <Plus className="h-4 w-4" />
                <span>New Chat</span>
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
