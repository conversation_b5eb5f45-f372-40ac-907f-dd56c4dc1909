import { 
  AgentConfig, 
  AgentSelectionCriteria,
  NotFoundError,
  AGENT_IDS,
  AGENT_CAPABILITIES,
  DEFAULT_MODEL_CONFIGS
} from '@waatch/shared';
import { prisma } from './database';
import { logger } from '../utils/logger';

export class AgentService {
  // Get all available agents
  async getAllAgents(): Promise<AgentConfig[]> {
    const agents = await prisma.agentConfig.findMany({
      where: { isActive: true },
      orderBy: { name: 'asc' },
    });

    return agents.map(this.mapPrismaToAgentConfig);
  }

  // Get agent by ID
  async getAgentById(agentId: string): Promise<AgentConfig> {
    const agent = await prisma.agentConfig.findUnique({
      where: { agentId, isActive: true },
    });

    if (!agent) {
      throw new NotFoundError(`Agent ${agentId}`);
    }

    return this.mapPrismaToAgentConfig(agent);
  }

  // Get agent capabilities and tools
  async getAgentCapabilities(agentId: string): Promise<{
    capabilities: string[];
    tools: any[];
    description: string;
  }> {
    const agent = await this.getAgentById(agentId);
    
    return {
      capabilities: agent.capabilities,
      tools: agent.tools,
      description: agent.description,
    };
  }

  // Recommend agents based on criteria
  async recommendAgents(criteria: AgentSelectionCriteria): Promise<{
    agent: AgentConfig;
    score: number;
    reasoning: string;
  }[]> {
    const allAgents = await this.getAllAgents();
    const recommendations = [];

    for (const agent of allAgents) {
      const score = this.calculateAgentScore(agent, criteria);
      const reasoning = this.generateRecommendationReasoning(agent, criteria, score);
      
      if (score > 0) {
        recommendations.push({
          agent,
          score,
          reasoning,
        });
      }
    }

    // Sort by score (highest first) and return top 5
    return recommendations
      .sort((a, b) => b.score - a.score)
      .slice(0, 5);
  }

  // Initialize default agents if they don't exist
  async initializeDefaultAgents(): Promise<void> {
    const existingAgents = await prisma.agentConfig.findMany();
    
    if (existingAgents.length === 0) {
      logger.info('Initializing default agents...');
      
      const defaultAgents = this.getDefaultAgentConfigs();
      
      for (const agentConfig of defaultAgents) {
        await prisma.agentConfig.create({
          data: {
            agentId: agentConfig.id,
            name: agentConfig.name,
            description: agentConfig.description,
            systemPrompt: agentConfig.systemPrompt,
            capabilities: agentConfig.capabilities,
            tools: agentConfig.tools,
            modelConfig: agentConfig.modelConfig,
            version: agentConfig.version,
            isActive: agentConfig.isActive,
          },
        });
      }
      
      logger.info(`Initialized ${defaultAgents.length} default agents`);
    }
  }

  // Calculate agent score based on criteria
  private calculateAgentScore(agent: AgentConfig, criteria: AgentSelectionCriteria): number {
    let score = 0;

    // Base score for active agents
    if (agent.isActive) {
      score += 10;
    }

    // Task-based scoring
    const taskLower = criteria.task.toLowerCase();
    
    // Check if agent capabilities match the task
    for (const capability of agent.capabilities) {
      if (taskLower.includes(capability.toLowerCase())) {
        score += 20;
      }
    }

    // Language-specific scoring
    if (criteria.language) {
      const languageLower = criteria.language.toLowerCase();
      if (agent.description.toLowerCase().includes(languageLower)) {
        score += 15;
      }
    }

    // Framework-specific scoring
    if (criteria.framework) {
      const frameworkLower = criteria.framework.toLowerCase();
      if (agent.description.toLowerCase().includes(frameworkLower)) {
        score += 15;
      }
    }

    // Complexity-based scoring
    if (criteria.complexity) {
      // Some agents are better for different complexity levels
      const complexityBonus = this.getComplexityBonus(agent.id, criteria.complexity);
      score += complexityBonus;
    }

    // Required capabilities scoring
    if (criteria.requiredCapabilities) {
      for (const required of criteria.requiredCapabilities) {
        if (agent.capabilities.includes(required)) {
          score += 25;
        }
      }
    }

    return Math.max(0, score);
  }

  // Generate reasoning for recommendation
  private generateRecommendationReasoning(
    agent: AgentConfig, 
    criteria: AgentSelectionCriteria, 
    score: number
  ): string {
    const reasons = [];

    if (agent.capabilities.some(cap => 
      criteria.task.toLowerCase().includes(cap.toLowerCase())
    )) {
      reasons.push('Capabilities match the task requirements');
    }

    if (criteria.language && 
        agent.description.toLowerCase().includes(criteria.language.toLowerCase())) {
      reasons.push(`Specialized in ${criteria.language}`);
    }

    if (criteria.framework && 
        agent.description.toLowerCase().includes(criteria.framework.toLowerCase())) {
      reasons.push(`Expert in ${criteria.framework}`);
    }

    if (score > 50) {
      reasons.push('Highly recommended for this type of task');
    } else if (score > 30) {
      reasons.push('Good fit for this task');
    } else {
      reasons.push('Can assist with this task');
    }

    return reasons.join('. ');
  }

  // Get complexity bonus for specific agents
  private getComplexityBonus(agentId: string, complexity: string): number {
    const complexityMap: Record<string, Record<string, number>> = {
      [AGENT_IDS.DEVIN_ENGINEER]: { high: 15, medium: 10, low: 5 },
      [AGENT_IDS.PAIR_PROGRAMMING]: { high: 10, medium: 15, low: 10 },
      [AGENT_IDS.DATABASE_EXPERT]: { high: 15, medium: 10, low: 5 },
      [AGENT_IDS.DEVOPS_EXPERT]: { high: 15, medium: 10, low: 5 },
      [AGENT_IDS.LOVABLE_EDITOR]: { high: 5, medium: 15, low: 10 },
    };

    return complexityMap[agentId]?.[complexity] || 0;
  }

  // Map Prisma model to AgentConfig type
  private mapPrismaToAgentConfig(prismaAgent: any): AgentConfig {
    return {
      id: prismaAgent.agentId,
      name: prismaAgent.name,
      description: prismaAgent.description,
      systemPrompt: prismaAgent.systemPrompt,
      capabilities: prismaAgent.capabilities as string[],
      tools: prismaAgent.tools as any[],
      modelConfig: prismaAgent.modelConfig as any,
      version: prismaAgent.version,
      isActive: prismaAgent.isActive,
    };
  }

  // Get default agent configurations
  private getDefaultAgentConfigs(): AgentConfig[] {
    return [
      {
        id: AGENT_IDS.PAIR_PROGRAMMING,
        name: 'Pair Programming Agent',
        description: 'Context-aware coding partner for iterative development',
        systemPrompt: 'You are a collaborative coding partner that helps with iterative development...',
        capabilities: [
          AGENT_CAPABILITIES.CODE_GENERATION,
          AGENT_CAPABILITIES.DEBUGGING,
          AGENT_CAPABILITIES.REFACTORING,
        ],
        tools: [
          {
            name: 'codebase_search',
            description: 'Search codebase with semantic understanding',
            schema: {},
            permissions: ['read:codebase'],
          },
          {
            name: 'edit_file',
            description: 'Edit files with AST validation',
            schema: {},
            permissions: ['write:files'],
          },
        ],
        modelConfig: DEFAULT_MODEL_CONFIGS.CHUTES_DEEPSEEK,
        version: '1.0.0',
        isActive: true,
      },
      {
        id: AGENT_IDS.DATABASE_EXPERT,
        name: 'DataForge - Database Expert',
        description: 'Database design, SQL optimization, and data modeling specialist',
        systemPrompt: 'You are DataForge, a specialized Database and SQL Expert Agent...',
        capabilities: [
          AGENT_CAPABILITIES.DATABASE_DESIGN,
          AGENT_CAPABILITIES.PERFORMANCE_OPTIMIZATION,
          AGENT_CAPABILITIES.API_DESIGN,
        ],
        tools: [
          {
            name: 'database_query',
            description: 'Execute and optimize database queries',
            schema: {},
            permissions: ['access:database'],
          },
          {
            name: 'schema_design',
            description: 'Design and validate database schemas',
            schema: {},
            permissions: ['access:database'],
          },
        ],
        modelConfig: DEFAULT_MODEL_CONFIGS.CHUTES_DEEPSEEK,
        version: '1.0.0',
        isActive: true,
      },
      // Add more default agents as needed...
    ];
  }
}
