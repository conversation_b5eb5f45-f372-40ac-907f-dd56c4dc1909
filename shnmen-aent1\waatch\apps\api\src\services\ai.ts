import { OpenAI } from 'openai';
import { 
  <PERSON><PERSON>onfig, 
  AgentContext, 
  AgentResponse,
  ExternalServiceError
} from '@waatch/shared';
import { config } from '../config/config';
import { logger } from '../utils/logger';

export class AIService {
  private chutesClient: OpenAI;
  private openaiClient?: OpenAI;

  constructor() {
    // Initialize Chutes AI client (OpenAI-compatible)
    this.chutesClient = new OpenAI({
      apiKey: config.ai.chutes.apiKey,
      baseURL: config.ai.chutes.baseUrl,
    });

    // Initialize OpenAI client as fallback
    if (config.ai.openai.apiKey) {
      this.openaiClient = new OpenAI({
        apiKey: config.ai.openai.apiKey,
      });
    }
  }

  // Generate AI response
  async generateResponse(
    message: string,
    agent: AgentConfig,
    context: AgentContext
  ): Promise<AgentResponse> {
    const startTime = Date.now();

    try {
      // Prepare messages for the AI model
      const messages = await this.prepareMessages(message, agent, context);

      // Choose the appropriate client and model
      const { client, model } = this.selectClientAndModel(agent);

      logger.info('Generating AI response', {
        agentId: agent.id,
        model,
        messageLength: message.length,
        contextSize: JSON.stringify(context).length,
      });

      // Generate response
      const completion = await client.chat.completions.create({
        model,
        messages,
        temperature: agent.modelConfig.temperature,
        max_tokens: agent.modelConfig.maxTokens,
        top_p: agent.modelConfig.topP,
        frequency_penalty: agent.modelConfig.frequencyPenalty,
        presence_penalty: agent.modelConfig.presencePenalty,
        stream: false,
      });

      const responseContent = completion.choices[0]?.message?.content || '';
      const tokensUsed = completion.usage?.total_tokens || 0;
      const processingTime = Date.now() - startTime;

      // Parse tool calls if any
      const toolCalls = this.parseToolCalls(responseContent);

      // Execute tools if needed
      const toolResults = await this.executeTools(toolCalls, context);

      const response: AgentResponse = {
        id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        agentId: agent.id,
        content: responseContent,
        toolCalls,
        metadata: {
          tokensUsed,
          processingTime,
          model,
        },
        timestamp: new Date(),
      };

      logger.info('AI response generated', {
        agentId: agent.id,
        responseLength: responseContent.length,
        tokensUsed,
        processingTime,
        toolCallsCount: toolCalls?.length || 0,
      });

      return response;

    } catch (error) {
      logger.error('AI response generation failed', {
        agentId: agent.id,
        error: error instanceof Error ? error.message : 'Unknown error',
        processingTime: Date.now() - startTime,
      });

      throw new ExternalServiceError(
        `Failed to generate AI response: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'ai_service'
      );
    }
  }

  // Generate streaming response
  async generateStreamingResponse(
    message: string,
    agent: AgentConfig,
    context: AgentContext,
    onChunk: (chunk: string) => void
  ): Promise<AgentResponse> {
    const startTime = Date.now();

    try {
      const messages = await this.prepareMessages(message, agent, context);
      const { client, model } = this.selectClientAndModel(agent);

      const stream = await client.chat.completions.create({
        model,
        messages,
        temperature: agent.modelConfig.temperature,
        max_tokens: agent.modelConfig.maxTokens,
        top_p: agent.modelConfig.topP,
        frequency_penalty: agent.modelConfig.frequencyPenalty,
        presence_penalty: agent.modelConfig.presencePenalty,
        stream: true,
      });

      let fullContent = '';
      let tokensUsed = 0;

      for await (const chunk of stream) {
        const content = chunk.choices[0]?.delta?.content || '';
        if (content) {
          fullContent += content;
          onChunk(content);
        }

        if (chunk.usage) {
          tokensUsed = chunk.usage.total_tokens;
        }
      }

      const processingTime = Date.now() - startTime;

      return {
        id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        agentId: agent.id,
        content: fullContent,
        metadata: {
          tokensUsed,
          processingTime,
          model,
        },
        timestamp: new Date(),
      };

    } catch (error) {
      logger.error('Streaming AI response failed', {
        agentId: agent.id,
        error: error instanceof Error ? error.message : 'Unknown error',
      });

      throw new ExternalServiceError(
        `Failed to generate streaming response: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'ai_service'
      );
    }
  }

  // Prepare messages for AI model
  private async prepareMessages(
    message: string,
    agent: AgentConfig,
    context: AgentContext
  ): Promise<Array<{ role: string; content: string }>> {
    const messages = [
      {
        role: 'system',
        content: this.buildSystemPrompt(agent, context),
      },
      {
        role: 'user',
        content: message,
      },
    ];

    // Add conversation history if available
    // TODO: Implement conversation history retrieval

    return messages;
  }

  // Build system prompt with context
  private buildSystemPrompt(agent: AgentConfig, context: AgentContext): string {
    let systemPrompt = agent.systemPrompt;

    // Add context information
    if (context.workspaceState.openFiles?.length > 0) {
      systemPrompt += `\n\nCurrent workspace context:`;
      systemPrompt += `\nOpen files: ${context.workspaceState.openFiles.join(', ')}`;
    }

    if (context.workspaceState.currentFile) {
      systemPrompt += `\nCurrent file: ${context.workspaceState.currentFile}`;
    }

    // Add available tools
    if (agent.tools.length > 0) {
      systemPrompt += `\n\nAvailable tools:`;
      agent.tools.forEach(tool => {
        systemPrompt += `\n- ${tool.name}: ${tool.description}`;
      });
    }

    return systemPrompt;
  }

  // Select appropriate client and model
  private selectClientAndModel(agent: AgentConfig): { client: OpenAI; model: string } {
    if (agent.modelConfig.provider === 'openai' && this.openaiClient) {
      return {
        client: this.openaiClient,
        model: agent.modelConfig.model,
      };
    }

    // Default to Chutes AI
    return {
      client: this.chutesClient,
      model: agent.modelConfig.model,
    };
  }

  // Parse tool calls from response
  private parseToolCalls(content: string): Array<{
    tool: string;
    parameters: Record<string, any>;
  }> | undefined {
    // Simple tool call parsing - can be enhanced
    const toolCallRegex = /```tool:(\w+)\n([\s\S]*?)```/g;
    const toolCalls = [];
    let match;

    while ((match = toolCallRegex.exec(content)) !== null) {
      try {
        const toolName = match[1];
        const parameters = JSON.parse(match[2]);
        toolCalls.push({
          tool: toolName,
          parameters,
        });
      } catch (error) {
        logger.warn('Failed to parse tool call', { match: match[0] });
      }
    }

    return toolCalls.length > 0 ? toolCalls : undefined;
  }

  // Execute tools
  private async executeTools(
    toolCalls: Array<{ tool: string; parameters: Record<string, any> }> | undefined,
    context: AgentContext
  ): Promise<any[] | undefined> {
    if (!toolCalls || toolCalls.length === 0) {
      return undefined;
    }

    const results = [];

    for (const toolCall of toolCalls) {
      try {
        // TODO: Implement actual tool execution
        const result = await this.executeTool(toolCall.tool, toolCall.parameters, context);
        results.push(result);
      } catch (error) {
        logger.error('Tool execution failed', {
          tool: toolCall.tool,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
        results.push({
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    }

    return results;
  }

  // Execute individual tool
  private async executeTool(
    toolName: string,
    parameters: Record<string, any>,
    context: AgentContext
  ): Promise<any> {
    // TODO: Implement actual tool execution based on agent tools
    logger.info('Tool execution requested', { toolName, parameters });
    
    // Placeholder implementation
    return {
      success: true,
      data: `Tool ${toolName} executed with parameters: ${JSON.stringify(parameters)}`,
    };
  }
}
